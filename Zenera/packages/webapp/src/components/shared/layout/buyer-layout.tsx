"use client"

import { ReactNode } from "react"
import BuyerHeader from "./buyer-header"
import BuyerFooter from "./buyer-footer"
import { ErrorBoundary } from "@/components/shared/error-boundary"
import { Toaster } from "sonner"

interface BuyerLayoutProps {
  children: ReactNode
  showHeader?: boolean
  showFooter?: boolean
  className?: string
}

export default function BuyerLayout({ 
  children, 
  showHeader = true, 
  showFooter = true,
  className = ""
}: BuyerLayoutProps) {
  return (
    <div className={`min-h-screen flex flex-col ${className}`}>
      {showHeader && <BuyerHeader />}
      
      <main className="flex-1">
        <ErrorBoundary>
          {children}
        </ErrorBoundary>
      </main>
      
      {showFooter && <BuyerFooter />}
      
      {/* Toast notifications */}
      <Toaster 
        position="top-right"
        richColors
        closeButton
      />
    </div>
  )
}
