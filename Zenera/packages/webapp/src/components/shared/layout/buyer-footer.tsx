"use client"

import { Facebook, Twitter, Instagram, Youtube, Mail, Phone, MapPin } from "lucide-react"
import Link from "next/link"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Separator } from "@/components/ui/separator"
import { useHTranslation } from "@/lib/i18n/hooks/useHTranslation"

export default function BuyerFooter() {
  const { t } = useHTranslation('ecommerce')

  const footerLinks = {
    about: [
      { label: t('footer.aboutUs'), href: '/about' },
      { label: t('footer.careers'), href: '/careers' },
      { label: t('footer.press'), href: '/press' },
      { label: t('footer.blog'), href: '/blog' },
    ],
    customerService: [
      { label: t('footer.contactUs'), href: '/contact' },
      { label: t('footer.faq'), href: '/faq' },
      { label: t('footer.shipping'), href: '/shipping' },
      { label: t('footer.returns'), href: '/returns' },
      { label: t('footer.sizeGuide'), href: '/size-guide' },
    ],
    quickLinks: [
      { label: t('footer.newArrivals'), href: '/new-arrivals' },
      { label: t('footer.bestSellers'), href: '/best-sellers' },
      { label: t('footer.saleItems'), href: '/sale' },
      { label: t('footer.giftCards'), href: '/gift-cards' },
    ],
    legal: [
      { label: t('footer.privacyPolicy'), href: '/privacy' },
      { label: t('footer.termsOfService'), href: '/terms' },
      { label: t('footer.cookiePolicy'), href: '/cookies' },
      { label: t('footer.refundPolicy'), href: '/refund' },
    ]
  }

  const handleNewsletterSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    // Handle newsletter subscription
    console.log('Newsletter subscription')
  }

  return (
    <footer className="bg-background border-t">
      {/* Newsletter section */}
      <div className="bg-muted/50">
        <div className="container mx-auto px-4 py-8">
          <div className="flex flex-col md:flex-row items-center justify-between space-y-4 md:space-y-0">
            <div className="text-center md:text-left">
              <h3 className="text-lg font-semibold mb-2">{t('footer.newsletter.title')}</h3>
              <p className="text-muted-foreground">{t('footer.newsletter.description')}</p>
            </div>
            <form onSubmit={handleNewsletterSubmit} className="flex w-full max-w-sm space-x-2">
              <Input
                type="email"
                placeholder={t('footer.newsletter.placeholder')}
                className="flex-1"
                required
              />
              <Button type="submit">{t('footer.newsletter.subscribe')}</Button>
            </form>
          </div>
        </div>
      </div>

      {/* Main footer content */}
      <div className="container mx-auto px-4 py-12">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-8">
          {/* Company info */}
          <div className="lg:col-span-2">
            <div className="flex items-center space-x-2 mb-4">
              <div className="h-8 w-8 rounded-lg bg-primary flex items-center justify-center">
                <span className="text-primary-foreground font-bold">Z</span>
              </div>
              <span className="text-xl font-bold">Zenera</span>
            </div>
            <p className="text-muted-foreground mb-4 max-w-md">
              {t('footer.description')}
            </p>
            
            {/* Contact info */}
            <div className="space-y-2 mb-6">
              <div className="flex items-center space-x-2 text-sm text-muted-foreground">
                <MapPin className="h-4 w-4" />
                <span>{t('footer.address')}</span>
              </div>
              <div className="flex items-center space-x-2 text-sm text-muted-foreground">
                <Phone className="h-4 w-4" />
                <span>+84 ***********</span>
              </div>
              <div className="flex items-center space-x-2 text-sm text-muted-foreground">
                <Mail className="h-4 w-4" />
                <span><EMAIL></span>
              </div>
            </div>

            {/* Social media */}
            <div className="flex space-x-4">
              <Button variant="ghost" size="icon" className="text-muted-foreground hover:text-primary">
                <Facebook className="h-5 w-5" />
              </Button>
              <Button variant="ghost" size="icon" className="text-muted-foreground hover:text-primary">
                <Twitter className="h-5 w-5" />
              </Button>
              <Button variant="ghost" size="icon" className="text-muted-foreground hover:text-primary">
                <Instagram className="h-5 w-5" />
              </Button>
              <Button variant="ghost" size="icon" className="text-muted-foreground hover:text-primary">
                <Youtube className="h-5 w-5" />
              </Button>
            </div>
          </div>

          {/* About */}
          <div>
            <h3 className="text-lg font-semibold mb-4">{t('footer.about')}</h3>
            <ul className="space-y-2">
              {footerLinks.about.map((link) => (
                <li key={link.href}>
                  <Link 
                    href={link.href}
                    className="text-muted-foreground hover:text-primary transition-colors"
                  >
                    {link.label}
                  </Link>
                </li>
              ))}
            </ul>
          </div>

          {/* Customer Service */}
          <div>
            <h3 className="text-lg font-semibold mb-4">{t('footer.customerService')}</h3>
            <ul className="space-y-2">
              {footerLinks.customerService.map((link) => (
                <li key={link.href}>
                  <Link 
                    href={link.href}
                    className="text-muted-foreground hover:text-primary transition-colors"
                  >
                    {link.label}
                  </Link>
                </li>
              ))}
            </ul>
          </div>

          {/* Quick Links */}
          <div>
            <h3 className="text-lg font-semibold mb-4">{t('footer.quickLinks')}</h3>
            <ul className="space-y-2">
              {footerLinks.quickLinks.map((link) => (
                <li key={link.href}>
                  <Link 
                    href={link.href}
                    className="text-muted-foreground hover:text-primary transition-colors"
                  >
                    {link.label}
                  </Link>
                </li>
              ))}
            </ul>
            
            <h4 className="text-md font-semibold mt-6 mb-3">{t('footer.legal')}</h4>
            <ul className="space-y-2">
              {footerLinks.legal.map((link) => (
                <li key={link.href}>
                  <Link 
                    href={link.href}
                    className="text-sm text-muted-foreground hover:text-primary transition-colors"
                  >
                    {link.label}
                  </Link>
                </li>
              ))}
            </ul>
          </div>
        </div>
      </div>

      {/* Bottom bar */}
      <Separator />
      <div className="container mx-auto px-4 py-6">
        <div className="flex flex-col md:flex-row items-center justify-between space-y-4 md:space-y-0">
          <p className="text-sm text-muted-foreground">
            &copy; 2025 Zenera. {t('footer.allRightsReserved')}
          </p>
          <div className="flex items-center space-x-4">
            <span className="text-sm text-muted-foreground">{t('footer.paymentMethods')}:</span>
            <div className="flex space-x-2">
              {/* Payment method icons - replace with actual payment icons */}
              <div className="h-6 w-10 bg-muted rounded border"></div>
              <div className="h-6 w-10 bg-muted rounded border"></div>
              <div className="h-6 w-10 bg-muted rounded border"></div>
              <div className="h-6 w-10 bg-muted rounded border"></div>
            </div>
          </div>
        </div>
      </div>
    </footer>
  )
}
