"use client"

import { useState, useEffect } from "react"
import { useParams } from "next/navigation"
import { Star, Heart, ShoppingCart, Minus, Plus, Share2, Truck, Shield, RotateCcw } from "lucide-react"
import Image from "next/image"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { useHTranslation } from "@/lib/i18n/hooks/useHTranslation"
import { useCartStore } from "@/store/cart"
import { toast } from "sonner"

// Mock product data - replace with actual API call
const mockProduct = {
  id: 1,
  name: "Wireless Bluetooth Headphones Premium",
  price: 99.99,
  originalPrice: 129.99,
  rating: 4.5,
  reviews: 128,
  images: [
    "/images/products/headphones-1.jpg",
    "/images/products/headphones-2.jpg",
    "/images/products/headphones-3.jpg",
    "/images/products/headphones-4.jpg"
  ],
  category: "Electronics",
  brand: "TechSound",
  sku: "TS-WBH-001",
  inStock: true,
  stockQuantity: 25,
  isNew: true,
  isSale: true,
  discount: 23,
  description: "Experience premium sound quality with our latest wireless Bluetooth headphones. Featuring advanced noise cancellation, 30-hour battery life, and comfortable over-ear design.",
  features: [
    "Active Noise Cancellation",
    "30-hour battery life",
    "Bluetooth 5.0 connectivity",
    "Premium leather ear cushions",
    "Quick charge: 5 min = 2 hours playback",
    "Built-in microphone for calls"
  ],
  specifications: {
    "Driver Size": "40mm",
    "Frequency Response": "20Hz - 20kHz",
    "Impedance": "32 ohms",
    "Weight": "250g",
    "Connectivity": "Bluetooth 5.0, 3.5mm jack",
    "Battery": "30 hours wireless, 40 hours wired"
  },
  shipping: {
    freeShipping: true,
    estimatedDays: "2-3 business days",
    returnPolicy: "30-day return policy"
  }
}

const mockReviews = [
  {
    id: 1,
    user: "John D.",
    rating: 5,
    date: "2024-01-15",
    comment: "Amazing sound quality! The noise cancellation works perfectly.",
    verified: true
  },
  {
    id: 2,
    user: "Sarah M.",
    rating: 4,
    date: "2024-01-10",
    comment: "Great headphones, very comfortable for long listening sessions.",
    verified: true
  },
  {
    id: 3,
    user: "Mike R.",
    rating: 5,
    date: "2024-01-08",
    comment: "Best purchase I've made this year. Highly recommended!",
    verified: false
  }
]

export default function ProductDetailPage() {
  const params = useParams()
  const { t } = useHTranslation('ecommerce')
  const { addItem } = useCartStore()
  const [product, setProduct] = useState(mockProduct)
  const [reviews, setReviews] = useState(mockReviews)
  const [selectedImage, setSelectedImage] = useState(0)
  const [quantity, setQuantity] = useState(1)
  const [isWishlisted, setIsWishlisted] = useState(false)

  // Load product data based on ID
  useEffect(() => {
    // In real app, fetch product data using params.id
    console.log('Loading product:', params.id)
  }, [params.id])

  const handleAddToCart = () => {
    addItem({
      id: product.id,
      name: product.name,
      price: product.price,
      quantity: quantity,
      image: product.images[0]
    })
    toast.success(t('cart.itemAdded', { name: product.name }))
  }

  const handleQuantityChange = (delta: number) => {
    const newQuantity = quantity + delta
    if (newQuantity >= 1 && newQuantity <= product.stockQuantity) {
      setQuantity(newQuantity)
    }
  }

  const handleWishlist = () => {
    setIsWishlisted(!isWishlisted)
    toast.success(isWishlisted ? t('wishlist.removed') : t('wishlist.added'))
  }

  const handleShare = () => {
    if (navigator.share) {
      navigator.share({
        title: product.name,
        text: product.description,
        url: window.location.href
      })
    } else {
      navigator.clipboard.writeText(window.location.href)
      toast.success(t('product.linkCopied'))
    }
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-12">
        {/* Product Images */}
        <div className="space-y-4">
          {/* Main image */}
          <div className="aspect-square bg-muted rounded-lg flex items-center justify-center">
            <span className="text-muted-foreground">Product Image</span>
          </div>
          
          {/* Thumbnail images */}
          <div className="grid grid-cols-4 gap-2">
            {product.images.map((_, index) => (
              <button
                key={index}
                onClick={() => setSelectedImage(index)}
                className={`aspect-square bg-muted rounded border-2 transition-colors ${
                  selectedImage === index ? 'border-primary' : 'border-transparent'
                }`}
              >
                <span className="text-xs text-muted-foreground">{index + 1}</span>
              </button>
            ))}
          </div>
        </div>

        {/* Product Info */}
        <div className="space-y-6">
          {/* Badges */}
          <div className="flex gap-2">
            {product.isNew && (
              <Badge variant="secondary">{t('product.new')}</Badge>
            )}
            {product.isSale && (
              <Badge variant="destructive">-{product.discount}%</Badge>
            )}
          </div>

          {/* Title and rating */}
          <div>
            <h1 className="text-3xl font-bold mb-2">{product.name}</h1>
            <div className="flex items-center gap-4 mb-2">
              <div className="flex items-center gap-1">
                {[...Array(5)].map((_, i) => (
                  <Star
                    key={i}
                    className={`h-4 w-4 ${
                      i < Math.floor(product.rating)
                        ? 'fill-yellow-400 text-yellow-400'
                        : 'text-muted-foreground'
                    }`}
                  />
                ))}
                <span className="ml-1">{product.rating}</span>
              </div>
              <span className="text-muted-foreground">
                ({product.reviews} {t('product.reviews')})
              </span>
            </div>
            <p className="text-muted-foreground">
              {t('product.brand')}: {product.brand} | {t('product.sku')}: {product.sku}
            </p>
          </div>

          {/* Price */}
          <div className="flex items-center gap-4">
            <span className="text-3xl font-bold">${product.price}</span>
            {product.originalPrice && (
              <span className="text-xl text-muted-foreground line-through">
                ${product.originalPrice}
              </span>
            )}
            {product.isSale && (
              <Badge variant="destructive" className="text-sm">
                {t('product.save')} ${(product.originalPrice! - product.price).toFixed(2)}
              </Badge>
            )}
          </div>

          {/* Description */}
          <p className="text-muted-foreground leading-relaxed">
            {product.description}
          </p>

          {/* Stock status */}
          <div className="flex items-center gap-2">
            <div className={`h-2 w-2 rounded-full ${product.inStock ? 'bg-green-500' : 'bg-red-500'}`} />
            <span className={product.inStock ? 'text-green-600' : 'text-red-600'}>
              {product.inStock 
                ? `${t('product.inStock')} (${product.stockQuantity} ${t('product.available')})`
                : t('product.outOfStock')
              }
            </span>
          </div>

          {/* Quantity and Add to Cart */}
          <div className="space-y-4">
            <div className="flex items-center gap-4">
              <label className="font-medium">{t('product.quantity')}:</label>
              <div className="flex items-center border rounded-md">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => handleQuantityChange(-1)}
                  disabled={quantity <= 1}
                >
                  <Minus className="h-4 w-4" />
                </Button>
                <span className="px-4 py-2 min-w-[3rem] text-center">{quantity}</span>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => handleQuantityChange(1)}
                  disabled={quantity >= product.stockQuantity}
                >
                  <Plus className="h-4 w-4" />
                </Button>
              </div>
            </div>

            <div className="flex gap-3">
              <Button 
                onClick={handleAddToCart}
                disabled={!product.inStock}
                className="flex-1"
                size="lg"
              >
                <ShoppingCart className="h-5 w-5 mr-2" />
                {t('product.addToCart')}
              </Button>
              <Button
                variant="outline"
                size="lg"
                onClick={handleWishlist}
                className={isWishlisted ? 'text-red-500 border-red-500' : ''}
              >
                <Heart className={`h-5 w-5 ${isWishlisted ? 'fill-current' : ''}`} />
              </Button>
              <Button variant="outline" size="lg" onClick={handleShare}>
                <Share2 className="h-5 w-5" />
              </Button>
            </div>
          </div>

          {/* Shipping info */}
          <Card>
            <CardContent className="p-4">
              <div className="space-y-3">
                <div className="flex items-center gap-3">
                  <Truck className="h-5 w-5 text-green-600" />
                  <div>
                    <p className="font-medium">{t('shipping.free')}</p>
                    <p className="text-sm text-muted-foreground">
                      {t('shipping.estimated')}: {product.shipping.estimatedDays}
                    </p>
                  </div>
                </div>
                <div className="flex items-center gap-3">
                  <RotateCcw className="h-5 w-5 text-blue-600" />
                  <div>
                    <p className="font-medium">{product.shipping.returnPolicy}</p>
                    <p className="text-sm text-muted-foreground">{t('shipping.returnDesc')}</p>
                  </div>
                </div>
                <div className="flex items-center gap-3">
                  <Shield className="h-5 w-5 text-purple-600" />
                  <div>
                    <p className="font-medium">{t('shipping.warranty')}</p>
                    <p className="text-sm text-muted-foreground">{t('shipping.warrantyDesc')}</p>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Product Details Tabs */}
      <Tabs defaultValue="features" className="w-full">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="features">{t('product.features')}</TabsTrigger>
          <TabsTrigger value="specifications">{t('product.specifications')}</TabsTrigger>
          <TabsTrigger value="reviews">{t('product.reviews')} ({reviews.length})</TabsTrigger>
        </TabsList>
        
        <TabsContent value="features" className="mt-6">
          <Card>
            <CardHeader>
              <CardTitle>{t('product.keyFeatures')}</CardTitle>
            </CardHeader>
            <CardContent>
              <ul className="space-y-2">
                {product.features.map((feature, index) => (
                  <li key={index} className="flex items-center gap-2">
                    <div className="h-1.5 w-1.5 rounded-full bg-primary" />
                    {feature}
                  </li>
                ))}
              </ul>
            </CardContent>
          </Card>
        </TabsContent>
        
        <TabsContent value="specifications" className="mt-6">
          <Card>
            <CardHeader>
              <CardTitle>{t('product.technicalSpecs')}</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {Object.entries(product.specifications).map(([key, value]) => (
                  <div key={key} className="flex justify-between py-2 border-b last:border-b-0">
                    <span className="font-medium">{key}:</span>
                    <span className="text-muted-foreground">{value}</span>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
        
        <TabsContent value="reviews" className="mt-6">
          <div className="space-y-6">
            {reviews.map((review) => (
              <Card key={review.id}>
                <CardContent className="p-6">
                  <div className="flex items-start justify-between mb-3">
                    <div>
                      <div className="flex items-center gap-2 mb-1">
                        <span className="font-medium">{review.user}</span>
                        {review.verified && (
                          <Badge variant="secondary" className="text-xs">
                            {t('reviews.verified')}
                          </Badge>
                        )}
                      </div>
                      <div className="flex items-center gap-2">
                        <div className="flex">
                          {[...Array(5)].map((_, i) => (
                            <Star
                              key={i}
                              className={`h-4 w-4 ${
                                i < review.rating
                                  ? 'fill-yellow-400 text-yellow-400'
                                  : 'text-muted-foreground'
                              }`}
                            />
                          ))}
                        </div>
                        <span className="text-sm text-muted-foreground">{review.date}</span>
                      </div>
                    </div>
                  </div>
                  <p className="text-muted-foreground">{review.comment}</p>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>
      </Tabs>
    </div>
  )
}
