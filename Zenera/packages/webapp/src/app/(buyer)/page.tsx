"use client"

import { useState, useEffect } from "react"
import { Star, Heart, ShoppingCart, Filter, Grid, List } from "lucide-react"
import Image from "next/image"
import Link from "next/link"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, CardContent, CardFooter } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Sheet, Sheet<PERSON>ontent, SheetHeader, SheetTitle, SheetTrigger } from "@/components/ui/sheet"
import { useHTranslation } from "@/lib/i18n/hooks/useHTranslation"
import { useCartStore } from "@/store/cart"
import { toast } from "sonner"

// Mock product data - replace with actual API call
const mockProducts = [
  {
    id: 1,
    name: "Wireless Bluetooth Headphones",
    price: 99.99,
    originalPrice: 129.99,
    rating: 4.5,
    reviews: 128,
    image: "/images/products/headphones.jpg",
    category: "Electronics",
    isNew: true,
    isSale: true,
    discount: 23
  },
  {
    id: 2,
    name: "Premium Cotton T-Shirt",
    price: 29.99,
    originalPrice: null,
    rating: 4.2,
    reviews: 89,
    image: "/images/products/tshirt.jpg",
    category: "Fashion",
    isNew: false,
    isSale: false,
    discount: 0
  },
  {
    id: 3,
    name: "Smart Watch Series 5",
    price: 299.99,
    originalPrice: 399.99,
    rating: 4.8,
    reviews: 256,
    image: "/images/products/smartwatch.jpg",
    category: "Electronics",
    isNew: true,
    isSale: true,
    discount: 25
  },
  {
    id: 4,
    name: "Leather Backpack",
    price: 79.99,
    originalPrice: null,
    rating: 4.3,
    reviews: 67,
    image: "/images/products/backpack.jpg",
    category: "Fashion",
    isNew: false,
    isSale: false,
    discount: 0
  },
  {
    id: 5,
    name: "Wireless Mouse",
    price: 39.99,
    originalPrice: 49.99,
    rating: 4.1,
    reviews: 143,
    image: "/images/products/mouse.jpg",
    category: "Electronics",
    isNew: false,
    isSale: true,
    discount: 20
  },
  {
    id: 6,
    name: "Running Shoes",
    price: 89.99,
    originalPrice: null,
    rating: 4.6,
    reviews: 201,
    image: "/images/products/shoes.jpg",
    category: "Sports",
    isNew: true,
    isSale: false,
    discount: 0
  }
]

const categories = [
  { id: 'all', name: 'All Categories' },
  { id: 'electronics', name: 'Electronics' },
  { id: 'fashion', name: 'Fashion' },
  { id: 'sports', name: 'Sports' },
  { id: 'home', name: 'Home & Garden' }
]

export default function ProductCatalogPage() {
  const { t } = useHTranslation('ecommerce')
  const { addItem } = useCartStore()
  const [products, setProducts] = useState(mockProducts)
  const [filteredProducts, setFilteredProducts] = useState(mockProducts)
  const [selectedCategory, setSelectedCategory] = useState('all')
  const [sortBy, setSortBy] = useState('featured')
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid')
  const [priceRange, setPriceRange] = useState({ min: '', max: '' })

  // Filter and sort products
  useEffect(() => {
    let filtered = [...products]

    // Filter by category
    if (selectedCategory !== 'all') {
      filtered = filtered.filter(product => 
        product.category.toLowerCase() === selectedCategory
      )
    }

    // Filter by price range
    if (priceRange.min || priceRange.max) {
      filtered = filtered.filter(product => {
        const price = product.price
        const min = priceRange.min ? parseFloat(priceRange.min) : 0
        const max = priceRange.max ? parseFloat(priceRange.max) : Infinity
        return price >= min && price <= max
      })
    }

    // Sort products
    switch (sortBy) {
      case 'price-low':
        filtered.sort((a, b) => a.price - b.price)
        break
      case 'price-high':
        filtered.sort((a, b) => b.price - a.price)
        break
      case 'rating':
        filtered.sort((a, b) => b.rating - a.rating)
        break
      case 'newest':
        filtered.sort((a, b) => (b.isNew ? 1 : 0) - (a.isNew ? 1 : 0))
        break
      default:
        // Featured - keep original order
        break
    }

    setFilteredProducts(filtered)
  }, [products, selectedCategory, sortBy, priceRange])

  const handleAddToCart = (product: any) => {
    addItem({
      id: product.id,
      name: product.name,
      price: product.price,
      quantity: 1,
      image: product.image
    })
    toast.success(t('cart.itemAdded', { name: product.name }))
  }

  const ProductCard = ({ product }: { product: any }) => (
    <Card className="group hover:shadow-lg transition-shadow duration-300">
      <CardContent className="p-0">
        <div className="relative overflow-hidden">
          <div className="aspect-square bg-muted flex items-center justify-center">
            <span className="text-muted-foreground">No Image</span>
          </div>
          
          {/* Badges */}
          <div className="absolute top-2 left-2 flex flex-col gap-1">
            {product.isNew && (
              <Badge variant="secondary">{t('product.new')}</Badge>
            )}
            {product.isSale && (
              <Badge variant="destructive">-{product.discount}%</Badge>
            )}
          </div>

          {/* Wishlist button */}
          <Button
            variant="ghost"
            size="icon"
            className="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity"
          >
            <Heart className="h-4 w-4" />
          </Button>

          {/* Quick add to cart */}
          <Button
            onClick={() => handleAddToCart(product)}
            className="absolute bottom-2 left-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity"
          >
            <ShoppingCart className="h-4 w-4 mr-2" />
            {t('product.addToCart')}
          </Button>
        </div>

        <div className="p-4">
          <Link href={`/products/${product.id}`}>
            <h3 className="font-medium text-sm mb-2 line-clamp-2 hover:text-primary">
              {product.name}
            </h3>
          </Link>
          
          <div className="flex items-center gap-1 mb-2">
            <div className="flex items-center">
              {[...Array(5)].map((_, i) => (
                <Star
                  key={i}
                  className={`h-3 w-3 ${
                    i < Math.floor(product.rating)
                      ? 'fill-yellow-400 text-yellow-400'
                      : 'text-muted-foreground'
                  }`}
                />
              ))}
            </div>
            <span className="text-xs text-muted-foreground">
              ({product.reviews})
            </span>
          </div>

          <div className="flex items-center gap-2">
            <span className="font-semibold">${product.price}</span>
            {product.originalPrice && (
              <span className="text-sm text-muted-foreground line-through">
                ${product.originalPrice}
              </span>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  )

  return (
    <div className="container mx-auto px-4 py-8">
      {/* Hero section */}
      <div className="bg-gradient-to-r from-primary/10 to-primary/5 rounded-lg p-8 mb-8">
        <div className="max-w-2xl">
          <h1 className="text-3xl font-bold mb-4">{t('catalog.title')}</h1>
          <p className="text-muted-foreground mb-6">{t('catalog.subtitle')}</p>
          <Button size="lg">{t('catalog.shopNow')}</Button>
        </div>
      </div>

      {/* Filters and sorting */}
      <div className="flex flex-col lg:flex-row gap-4 mb-6">
        <div className="flex-1 flex flex-col sm:flex-row gap-4">
          {/* Category filter */}
          <Select value={selectedCategory} onValueChange={setSelectedCategory}>
            <SelectTrigger className="w-full sm:w-48">
              <SelectValue placeholder={t('filters.category')} />
            </SelectTrigger>
            <SelectContent>
              {categories.map((category) => (
                <SelectItem key={category.id} value={category.id}>
                  {category.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>

          {/* Sort */}
          <Select value={sortBy} onValueChange={setSortBy}>
            <SelectTrigger className="w-full sm:w-48">
              <SelectValue placeholder={t('filters.sortBy')} />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="featured">{t('filters.featured')}</SelectItem>
              <SelectItem value="newest">{t('filters.newest')}</SelectItem>
              <SelectItem value="price-low">{t('filters.priceLowHigh')}</SelectItem>
              <SelectItem value="price-high">{t('filters.priceHighLow')}</SelectItem>
              <SelectItem value="rating">{t('filters.rating')}</SelectItem>
            </SelectContent>
          </Select>

          {/* Mobile filters */}
          <Sheet>
            <SheetTrigger asChild>
              <Button variant="outline" className="lg:hidden">
                <Filter className="h-4 w-4 mr-2" />
                {t('filters.filters')}
              </Button>
            </SheetTrigger>
            <SheetContent side="left">
              <SheetHeader>
                <SheetTitle>{t('filters.filters')}</SheetTitle>
              </SheetHeader>
              <div className="space-y-4 mt-6">
                <div>
                  <label className="text-sm font-medium mb-2 block">
                    {t('filters.priceRange')}
                  </label>
                  <div className="flex gap-2">
                    <Input
                      type="number"
                      placeholder="Min"
                      value={priceRange.min}
                      onChange={(e) => setPriceRange(prev => ({ ...prev, min: e.target.value }))}
                    />
                    <Input
                      type="number"
                      placeholder="Max"
                      value={priceRange.max}
                      onChange={(e) => setPriceRange(prev => ({ ...prev, max: e.target.value }))}
                    />
                  </div>
                </div>
              </div>
            </SheetContent>
          </Sheet>
        </div>

        {/* View mode and results count */}
        <div className="flex items-center gap-4">
          <span className="text-sm text-muted-foreground">
            {filteredProducts.length} {t('catalog.results')}
          </span>
          
          <div className="flex items-center border rounded-md">
            <Button
              variant={viewMode === 'grid' ? 'default' : 'ghost'}
              size="sm"
              onClick={() => setViewMode('grid')}
            >
              <Grid className="h-4 w-4" />
            </Button>
            <Button
              variant={viewMode === 'list' ? 'default' : 'ghost'}
              size="sm"
              onClick={() => setViewMode('list')}
            >
              <List className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </div>

      {/* Products grid */}
      <div className={`grid gap-6 ${
        viewMode === 'grid' 
          ? 'grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4' 
          : 'grid-cols-1'
      }`}>
        {filteredProducts.map((product) => (
          <ProductCard key={product.id} product={product} />
        ))}
      </div>

      {/* Load more */}
      {filteredProducts.length > 0 && (
        <div className="text-center mt-12">
          <Button variant="outline" size="lg">
            {t('catalog.loadMore')}
          </Button>
        </div>
      )}

      {/* No results */}
      {filteredProducts.length === 0 && (
        <div className="text-center py-12">
          <p className="text-muted-foreground mb-4">{t('catalog.noResults')}</p>
          <Button onClick={() => {
            setSelectedCategory('all')
            setPriceRange({ min: '', max: '' })
          }}>
            {t('catalog.clearFilters')}
          </Button>
        </div>
      )}
    </div>
  )
}
