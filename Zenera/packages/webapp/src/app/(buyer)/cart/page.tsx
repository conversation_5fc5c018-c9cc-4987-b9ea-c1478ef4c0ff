"use client"

import { useState } from "react"
import { Minus, Plus, Trash2, Shopping<PERSON>ag, ArrowLeft } from "lucide-react"
import Image from "next/image"
import Link from "next/link"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Separator } from "@/components/ui/separator"
import { Badge } from "@/components/ui/badge"
import { useHTranslation } from "@/lib/i18n/hooks/useHTranslation"
import { useCartStore } from "@/store/cart"
import { toast } from "sonner"

export default function CartPage() {
  const { t } = useHTranslation('ecommerce')
  const { items, updateQuantity, removeItem, clearCart, getSubtotal, getTotalItems } = useCartStore()
  const [promoCode, setPromoCode] = useState("")
  const [appliedPromo, setAppliedPromo] = useState<{ code: string; discount: number } | null>(null)

  const shipping = 0 // Free shipping
  const tax = getSubtotal() * 0.1 // 10% tax
  const discount = appliedPromo ? (getSubtotal() * appliedPromo.discount / 100) : 0
  const total = getSubtotal() + shipping + tax - discount

  const handleQuantityChange = (itemId: number, newQuantity: number) => {
    if (newQuantity <= 0) {
      removeItem(itemId)
      toast.success(t('cart.itemRemoved'))
    } else {
      updateQuantity(itemId, newQuantity)
    }
  }

  const handleRemoveItem = (itemId: number) => {
    removeItem(itemId)
    toast.success(t('cart.itemRemoved'))
  }

  const handleApplyPromo = () => {
    // Mock promo codes
    const promoCodes = {
      'SAVE10': { discount: 10, description: '10% off' },
      'WELCOME20': { discount: 20, description: '20% off for new customers' },
      'FREESHIP': { discount: 0, description: 'Free shipping' }
    }

    const promo = promoCodes[promoCode.toUpperCase() as keyof typeof promoCodes]
    if (promo) {
      setAppliedPromo({ code: promoCode.toUpperCase(), discount: promo.discount })
      toast.success(t('cart.promoApplied', { code: promoCode.toUpperCase() }))
    } else {
      toast.error(t('cart.invalidPromo'))
    }
  }

  const handleRemovePromo = () => {
    setAppliedPromo(null)
    setPromoCode("")
    toast.success(t('cart.promoRemoved'))
  }

  if (items.length === 0) {
    return (
      <div className="container mx-auto px-4 py-16">
        <div className="max-w-md mx-auto text-center">
          <ShoppingBag className="h-24 w-24 mx-auto text-muted-foreground mb-6" />
          <h1 className="text-2xl font-bold mb-4">{t('cart.empty')}</h1>
          <p className="text-muted-foreground mb-8">{t('cart.emptyDescription')}</p>
          <Button asChild size="lg">
            <Link href="/">
              <ArrowLeft className="h-4 w-4 mr-2" />
              {t('cart.continueShopping')}
            </Link>
          </Button>
        </div>
      </div>
    )
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="flex items-center gap-4 mb-8">
        <Button variant="ghost" asChild>
          <Link href="/">
            <ArrowLeft className="h-4 w-4 mr-2" />
            {t('cart.continueShopping')}
          </Link>
        </Button>
        <h1 className="text-3xl font-bold">{t('cart.title')}</h1>
        <Badge variant="secondary">{getTotalItems()} {t('cart.items')}</Badge>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* Cart Items */}
        <div className="lg:col-span-2 space-y-4">
          {items.map((item) => (
            <Card key={item.id}>
              <CardContent className="p-6">
                <div className="flex gap-4">
                  {/* Product Image */}
                  <div className="w-24 h-24 bg-muted rounded-lg flex items-center justify-center flex-shrink-0">
                    <span className="text-xs text-muted-foreground">IMG</span>
                  </div>

                  {/* Product Info */}
                  <div className="flex-1 min-w-0">
                    <Link href={`/products/${item.id}`}>
                      <h3 className="font-medium text-lg mb-2 hover:text-primary line-clamp-2">
                        {item.name}
                      </h3>
                    </Link>
                    <p className="text-muted-foreground text-sm mb-4">
                      {t('product.sku')}: {item.id}
                    </p>
                    
                    {/* Quantity and Price */}
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-3">
                        <span className="text-sm font-medium">{t('product.quantity')}:</span>
                        <div className="flex items-center border rounded-md">
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleQuantityChange(item.id, item.quantity - 1)}
                          >
                            <Minus className="h-4 w-4" />
                          </Button>
                          <span className="px-3 py-1 min-w-[2.5rem] text-center">
                            {item.quantity}
                          </span>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleQuantityChange(item.id, item.quantity + 1)}
                          >
                            <Plus className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>
                      
                      <div className="flex items-center gap-4">
                        <span className="font-semibold text-lg">
                          ${(item.price * item.quantity).toFixed(2)}
                        </span>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleRemoveItem(item.id)}
                          className="text-destructive hover:text-destructive"
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}

          {/* Clear Cart */}
          <div className="flex justify-end">
            <Button
              variant="outline"
              onClick={() => {
                clearCart()
                toast.success(t('cart.cleared'))
              }}
              className="text-destructive border-destructive hover:bg-destructive hover:text-destructive-foreground"
            >
              <Trash2 className="h-4 w-4 mr-2" />
              {t('cart.clearAll')}
            </Button>
          </div>
        </div>

        {/* Order Summary */}
        <div className="space-y-6">
          {/* Promo Code */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">{t('cart.promoCode')}</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {appliedPromo ? (
                <div className="flex items-center justify-between p-3 bg-green-50 border border-green-200 rounded-md">
                  <div>
                    <p className="font-medium text-green-800">{appliedPromo.code}</p>
                    <p className="text-sm text-green-600">
                      {appliedPromo.discount}% {t('cart.discount')}
                    </p>
                  </div>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={handleRemovePromo}
                    className="text-green-700 hover:text-green-800"
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </div>
              ) : (
                <div className="flex gap-2">
                  <Input
                    placeholder={t('cart.enterPromoCode')}
                    value={promoCode}
                    onChange={(e) => setPromoCode(e.target.value)}
                  />
                  <Button onClick={handleApplyPromo} disabled={!promoCode.trim()}>
                    {t('cart.apply')}
                  </Button>
                </div>
              )}
              
              {/* Promo suggestions */}
              <div className="text-xs text-muted-foreground">
                <p>{t('cart.tryPromoCodes')}: SAVE10, WELCOME20</p>
              </div>
            </CardContent>
          </Card>

          {/* Order Summary */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">{t('cart.orderSummary')}</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <div className="flex justify-between">
                  <span>{t('cart.subtotal')}</span>
                  <span>${getSubtotal().toFixed(2)}</span>
                </div>
                
                <div className="flex justify-between">
                  <span>{t('cart.shipping')}</span>
                  <span className="text-green-600">
                    {shipping === 0 ? t('cart.free') : `$${shipping.toFixed(2)}`}
                  </span>
                </div>
                
                <div className="flex justify-between">
                  <span>{t('cart.tax')}</span>
                  <span>${tax.toFixed(2)}</span>
                </div>
                
                {appliedPromo && discount > 0 && (
                  <div className="flex justify-between text-green-600">
                    <span>{t('cart.discount')} ({appliedPromo.code})</span>
                    <span>-${discount.toFixed(2)}</span>
                  </div>
                )}
              </div>
              
              <Separator />
              
              <div className="flex justify-between text-lg font-semibold">
                <span>{t('cart.total')}</span>
                <span>${total.toFixed(2)}</span>
              </div>
              
              <Button className="w-full" size="lg" asChild>
                <Link href="/checkout">
                  {t('cart.proceedToCheckout')}
                </Link>
              </Button>
              
              <p className="text-xs text-muted-foreground text-center">
                {t('cart.secureCheckout')}
              </p>
            </CardContent>
          </Card>

          {/* Shipping Info */}
          <Card>
            <CardContent className="p-4">
              <div className="space-y-2 text-sm">
                <div className="flex items-center gap-2">
                  <div className="h-2 w-2 rounded-full bg-green-500" />
                  <span>{t('shipping.freeShipping')}</span>
                </div>
                <div className="flex items-center gap-2">
                  <div className="h-2 w-2 rounded-full bg-blue-500" />
                  <span>{t('shipping.fastDelivery')}</span>
                </div>
                <div className="flex items-center gap-2">
                  <div className="h-2 w-2 rounded-full bg-purple-500" />
                  <span>{t('shipping.securePayment')}</span>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}
