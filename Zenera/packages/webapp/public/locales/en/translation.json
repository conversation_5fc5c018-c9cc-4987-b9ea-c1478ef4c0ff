{"app": {"name": "Zenera", "description": "Modern E-commerce Platform", "welcome": "Welcome to Zenera", "loading": "Loading...", "error": "An error occurred", "success": "Success", "warning": "Warning", "info": "Information"}, "navigation": {"home": "Home", "products": "Products", "categories": "Categories", "cart": "<PERSON><PERSON>", "account": "Account", "login": "<PERSON><PERSON>", "register": "Register", "logout": "Logout", "dashboard": "Dashboard", "orders": "Orders", "profile": "Profile", "settings": "Settings", "help": "Help", "contact": "Contact", "about": "About"}, "actions": {"save": "Save", "cancel": "Cancel", "delete": "Delete", "edit": "Edit", "view": "View", "add": "Add", "remove": "Remove", "update": "Update", "create": "Create", "submit": "Submit", "reset": "Reset", "clear": "Clear", "search": "Search", "filter": "Filter", "sort": "Sort", "export": "Export", "import": "Import", "download": "Download", "upload": "Upload", "copy": "Copy", "share": "Share", "print": "Print", "refresh": "Refresh", "back": "Back", "next": "Next", "previous": "Previous", "continue": "Continue", "finish": "Finish", "close": "Close", "open": "Open", "expand": "Expand", "collapse": "Collapse", "select": "Select", "deselect": "Deselect", "confirm": "Confirm", "approve": "Approve", "reject": "Reject", "send": "Send", "receive": "Receive"}, "status": {"active": "Active", "inactive": "Inactive", "pending": "Pending", "approved": "Approved", "rejected": "Rejected", "completed": "Completed", "cancelled": "Cancelled", "processing": "Processing", "shipped": "Shipped", "delivered": "Delivered", "returned": "Returned", "refunded": "Refunded", "draft": "Draft", "published": "Published", "archived": "Archived", "deleted": "Deleted"}, "time": {"now": "Now", "today": "Today", "yesterday": "Yesterday", "tomorrow": "Tomorrow", "thisWeek": "This week", "lastWeek": "Last week", "nextWeek": "Next week", "thisMonth": "This month", "lastMonth": "Last month", "nextMonth": "Next month", "thisYear": "This year", "lastYear": "Last year", "nextYear": "Next year", "minute": "minute", "minutes": "minutes", "hour": "hour", "hours": "hours", "day": "day", "days": "days", "week": "week", "weeks": "weeks", "month": "month", "months": "months", "year": "year", "years": "years", "ago": "ago", "in": "in"}, "messages": {"noData": "No data available", "noResults": "No results found", "emptyList": "List is empty", "loadingData": "Loading data...", "savingData": "Saving data...", "dataUpdated": "Data updated successfully", "dataSaved": "Data saved successfully", "dataDeleted": "Data deleted successfully", "operationCompleted": "Operation completed successfully", "operationFailed": "Operation failed", "confirmDelete": "Are you sure you want to delete this item?", "confirmAction": "Are you sure you want to perform this action?", "unsavedChanges": "You have unsaved changes. Do you want to save them?", "sessionExpired": "Your session has expired. Please login again.", "accessDenied": "Access denied. You don't have permission to perform this action.", "networkError": "Network error. Please check your connection and try again.", "serverError": "Server error. Please try again later.", "validationError": "Please check the form data and try again.", "fileUploadError": "File upload failed. Please try again.", "fileTooLarge": "File is too large. Maximum size is {{maxSize}}.", "invalidFileType": "Invalid file type. Allowed types: {{allowedTypes}}.", "required": "This field is required", "invalidEmail": "Please enter a valid email address", "invalidPhone": "Please enter a valid phone number", "passwordTooShort": "Password must be at least {{minLength}} characters", "passwordMismatch": "Passwords do not match"}, "language": {"current": "Current language", "select": "Select language", "english": "English", "vietnamese": "Vietnamese", "change": "Change language", "changed": "Language changed to {{language}}"}, "currency": {"vnd": "VND", "usd": "USD", "eur": "EUR", "symbol": {"vnd": "₫", "usd": "$", "eur": "€"}}, "units": {"piece": "piece", "pieces": "pieces", "item": "item", "items": "items", "kg": "kg", "gram": "gram", "liter": "liter", "meter": "meter", "cm": "cm", "mm": "mm"}, "ecommerce": {"header": {"sellerChannel": "Seller Channel", "support": "Support", "categories": "Categories", "searchPlaceholder": "Search products...", "cart": "<PERSON><PERSON>", "viewCart": "View Cart", "profile": "Profile", "orders": "Orders", "wishlist": "Wishlist", "logout": "Logout", "login": "<PERSON><PERSON>", "register": "Register"}, "footer": {"newsletter": {"title": "Subscribe to our Newsletter", "description": "Get the latest updates on new products and upcoming sales", "placeholder": "Enter your email", "subscribe": "Subscribe"}, "description": "Your trusted e-commerce platform for quality products at great prices.", "address": "123 Main Street, Ho Chi Minh City, Vietnam", "about": "About", "aboutUs": "About Us", "careers": "Careers", "press": "Press", "blog": "Blog", "customerService": "Customer Service", "contactUs": "Contact Us", "faq": "FAQ", "shipping": "Shipping Info", "returns": "Returns", "sizeGuide": "Size Guide", "quickLinks": "Quick Links", "newArrivals": "New Arrivals", "bestSellers": "Best Sellers", "saleItems": "Sale Items", "giftCards": "Gift Cards", "legal": "Legal", "privacyPolicy": "Privacy Policy", "termsOfService": "Terms of Service", "cookiePolicy": "<PERSON><PERSON>", "refundPolicy": "Refund Policy", "allRightsReserved": "All rights reserved", "paymentMethods": "Payment Methods"}, "catalog": {"title": "Discover Amazing Products", "subtitle": "Find everything you need at unbeatable prices", "shopNow": "Shop Now", "results": "results", "loadMore": "Load More", "noResults": "No products found", "clearFilters": "Clear Filters"}, "filters": {"category": "Category", "sortBy": "Sort By", "featured": "Featured", "newest": "Newest", "priceLowHigh": "Price: Low to High", "priceHighLow": "Price: High to Low", "rating": "Rating", "filters": "Filters", "priceRange": "Price Range"}, "categories": {"electronics": "Electronics", "fashion": "Fashion", "home": "Home & Garden", "books": "Books", "sports": "Sports"}, "product": {"name": "Product Name", "description": "Description", "price": "Price", "comparePrice": "Compare Price", "costPrice": "Cost Price", "category": "Category", "brand": "Brand", "sku": "SKU", "barcode": "Barcode", "stockQuantity": "Stock Quantity", "lowStockThreshold": "Low Stock Threshold", "trackQuantity": "Track Quantity", "inventoryStatus": "Inventory Status", "weight": "Weight", "dimensions": "Dimensions", "length": "Length", "width": "<PERSON><PERSON><PERSON>", "height": "Height", "requiresShipping": "Requires Shipping", "images": "Images", "videos": "Videos", "seoTitle": "SEO Title", "seoDescription": "SEO Description", "tags": "Tags", "status": "Status", "featured": "Featured", "digital": "Digital Product", "new": "New", "addToCart": "Add to Cart", "quantity": "Quantity", "inStock": "In Stock", "outOfStock": "Out of Stock", "available": "available", "save": "Save", "reviews": "reviews", "features": "Features", "specifications": "Specifications", "keyFeatures": "Key Features", "technicalSpecs": "Technical Specifications", "linkCopied": "Link copied to clipboard"}, "cart": {"title": "Shopping Cart", "empty": "Your cart is empty", "emptyDescription": "Looks like you haven't added anything to your cart yet", "continueShopping": "Continue Shopping", "items": "items", "itemAdded": "{{name}} added to cart", "itemRemoved": "Item removed from cart", "cleared": "<PERSON><PERSON> cleared", "promoCode": "Promo Code", "enterPromoCode": "Enter promo code", "apply": "Apply", "promoApplied": "Promo code {{code}} applied", "promoRemoved": "Promo code removed", "invalidPromo": "Invalid promo code", "tryPromoCodes": "Try codes", "orderSummary": "Order Summary", "subtotal": "Subtotal", "shipping": "Shipping", "tax": "Tax", "discount": "Discount", "total": "Total", "free": "Free", "proceedToCheckout": "Proceed to Checkout", "secureCheckout": "Secure checkout with SSL encryption", "clearAll": "Clear All"}, "shipping": {"free": "Free Shipping", "freeShipping": "Free shipping on orders over $50", "fastDelivery": "Fast delivery in 2-3 business days", "securePayment": "Secure payment processing", "estimated": "Estimated delivery", "returnDesc": "Easy returns within 30 days", "warranty": "1 Year Warranty", "warrantyDesc": "Manufacturer warranty included"}, "auth": {"welcomeBack": "Welcome Back", "loginDescription": "Sign in to your account to continue", "createAccount": "Create Account", "registerDescription": "Join us today and start shopping", "email": "Email", "emailPlaceholder": "Enter your email", "password": "Password", "passwordPlaceholder": "Enter your password", "confirmPassword": "Confirm Password", "confirmPasswordPlaceholder": "Confirm your password", "firstName": "First Name", "firstNamePlaceholder": "Enter your first name", "lastName": "Last Name", "lastNamePlaceholder": "Enter your last name", "phone": "Phone", "phonePlaceholder": "Enter your phone number", "optional": "optional", "accountType": "Account Type", "selectAccountType": "Select account type", "buyer": "Buyer", "seller": "<PERSON><PERSON>", "rememberMe": "Remember me", "forgotPassword": "Forgot password?", "signIn": "Sign In", "signingIn": "Signing in...", "signUp": "Sign Up", "creatingAccount": "Creating account...", "orContinueWith": "Or continue with", "noAccount": "Don't have an account?", "alreadyHaveAccount": "Already have an account?", "agreeToTerms": "I agree to the", "termsOfService": "Terms of Service", "and": "and", "privacyPolicy": "Privacy Policy", "subscribeNewsletter": "Subscribe to newsletter for updates", "loginSuccess": "Login successful", "loginError": "<PERSON><PERSON> failed. Please check your credentials.", "registerSuccess": "Account created successfully", "registerError": "Registration failed. Please try again.", "socialLoginNotImplemented": "{{provider}} login not implemented yet", "validation": {"firstNameRequired": "First name is required", "lastNameRequired": "Last name is required", "emailRequired": "Email is required", "emailInvalid": "Please enter a valid email", "passwordMinLength": "Password must be at least 8 characters", "passwordMismatch": "Passwords do not match", "termsRequired": "You must agree to the terms and conditions"}}, "profile": {"displayName": "Display Name", "dateOfBirth": "Date of Birth", "gender": "Gender", "avatar": "Avatar", "preferences": "Preferences", "emailNotifications": "Email Notifications", "smsNotifications": "SMS Notifications", "marketingEmails": "Marketing Emails", "language": "Language"}, "wishlist": {"added": "Added to wishlist", "removed": "Removed from wishlist"}, "reviews": {"verified": "Verified Purchase"}, "order": {"orderNumber": "Order Number", "status": "Order Status", "paymentStatus": "Payment Status", "customer": "Customer", "shippingAddress": "Shipping Address", "billingAddress": "Billing Address", "paymentMethod": "Payment Method", "shippingMethod": "Shipping Method", "subtotal": "Subtotal", "shippingFee": "Shipping Fee", "discountAmount": "Discount Amount", "totalAmount": "Total Amount", "notes": "Notes", "adminNotes": "Admin Notes"}, "seller": {"personalInfo": "Personal Information", "storeInfo": "Store Information", "businessAddress": "Business Address", "bankInfo": "Bank Information", "documents": "Documents", "agreements": "Terms & Agreements", "storeName": "Store Name", "storeDescription": "Store Description", "businessType": "Business Type", "businessLicense": "Business License", "mainCategory": "Main Category", "accountHolderName": "Account Holder Name", "accountNumber": "Account Number", "bankName": "Bank Name", "branchName": "Branch Name"}, "review": {"rating": "Rating", "title": "Review Title", "content": "Review Content", "images": "Review Images", "anonymous": "Anonymous Review"}, "checkout": {"contactEmail": "Contact Email", "subscribeNewsletter": "Subscribe to Newsletter", "firstName": "First Name", "lastName": "Last Name", "company": "Company", "address1": "Address Line 1", "address2": "Address Line 2", "city": "City", "province": "Province", "postalCode": "Postal Code", "phone": "Phone Number", "billingSameAsShipping": "Billing same as shipping", "orderNotes": "Order Notes"}}, "pagination": {"page": "Page", "of": "of", "total": "Total", "showing": "Showing", "to": "to", "entries": "entries", "first": "First", "last": "Last", "previous": "Previous", "next": "Next", "itemsPerPage": "Items per page", "goToPage": "Go to page"}, "theme": {"light": "Light", "dark": "Dark", "system": "System", "toggle": "Toggle theme"}}